// Define variables for easy customization
$transition-duration: 0.3s;
$toggle-button-bg: #ec4899; // pink-500 from your example

.ava-sidebar-container {
  position: relative;
  height: 100vh;
  // Use CSS custom properties passed from the component
  width: var(--sidebar-width);
  transition: width $transition-duration ease-in-out;
  display: flex;

  // Handle positioning
  &.right-positioned {
    flex-direction: row-reverse;
    .ava-sidebar-toggle {
      right: auto;
      left: -1.25rem; // Position outside to the left
    }
  }

  // Handle collapsed state
  &.collapsed {
    width: var(--collapsed-width);

    .ava-sidebar {
      align-items: center; // Center content when collapsed
    }
  }
}

.ava-sidebar {
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden; // Hide content that overflows during transition
}

.ava-sidebar-toggle {
  position: absolute;
  top: 13rem; // 52 * 4px = 208px, from your example
  right: -1.25rem; // Position button slightly outside the sidebar
  z-index: 10;
  
  // Assuming ava-button renders a <button> tag
  button {
    background-color: $toggle-button-bg;
    color: white;
    border-radius: 9999px; // rounded-full
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}