export type SidebarSize = 'small' | 'medium' | 'large';
export type SidebarPosition = 'left' | 'right';
export type SidebarCollapseButtonVariant = 'inside' | 'outside';

export interface SidebarConfig {
  size?: SidebarSize;
  position?: SidebarPosition;
  collapsible?: boolean;
  collapseButtonVariant?: SidebarCollapseButtonVariant;
  showHeader?: boolean;
  showFooter?: boolean;
  customWidth?: string;
  customCollapsedWidth?: string;
  height?: string;
}

export interface SidebarSizeConfig {
  width: string;
  collapsedWidth: string;
  padding: string;
  headerPadding: string;
  footerPadding: string;
  contentPadding: string;
}

export const SIDEBAR_SIZE_CONFIGS: Record<SidebarSize, SidebarSizeConfig> = {
  small: {
    width: '240px',
    collapsedWidth: '60px',
    padding: '0.75rem',
    headerPadding: '1rem',
    footerPadding: '1rem',
    contentPadding: '0.75rem'
  },
  medium: {
    width: '280px',
    collapsedWidth: '70px',
    padding: '1rem',
    headerPadding: '1.5rem',
    footerPadding: '1.5rem',
    contentPadding: '1rem'
  },
  large: {
    width: '384px', // 24rem - matches your React example
    collapsedWidth: '80px', // 5rem - matches your React example
    padding: '1.5rem',
    headerPadding: '2rem',
    footerPadding: '2rem',
    contentPadding: '1.5rem'
  }
};

export interface SidebarNavigationItem {
  id: string;
  icon?: string;
  label: string;
  route?: string;
  active?: boolean;
  disabled?: boolean;
  badge?: string | number;
  children?: SidebarNavigationItem[];
}

export interface SidebarUserProfile {
  name: string;
  avatar?: string;
  subtitle?: string;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}
