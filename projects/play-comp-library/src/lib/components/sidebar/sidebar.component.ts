import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter, ViewEncapsulation } from '@angular/core';
import { ButtonComponent } from '../button/button.component'; // Assuming you have a button component

@Component({
  selector: 'ava-sidebar',
  standalone: true, // Modern standalone component
  imports: [CommonModule, ButtonComponent],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None, // To allow parent styles to pierce through easily
})
export class SidebarComponent {
  @Input() width: string = '24rem'; // 384px, from your example
  @Input() collapsedWidth: string = '5rem'; // 80px, from your example
  @Input() position: 'left' | 'right' = 'left';
  @Input() showToggleButton: boolean = true;

  // Use two-way data binding for the collapsed state
  @Input() isCollapsed: boolean = false;
  @Output() isCollapsedChange = new EventEmitter<boolean>();

  toggleCollapse(): void {
    this.isCollapsed = !this.isCollapsed;
    this.isCollapsedChange.emit(this.isCollapsed);
  }

  get collapseButtonIcon(): string {
    if (this.position === 'right') {
      return this.isCollapsed ? 'ChevronLeft' : 'ChevronRight';
    }
    return this.isCollapsed ? 'ChevronRight' : 'ChevronLeft';
  }
}