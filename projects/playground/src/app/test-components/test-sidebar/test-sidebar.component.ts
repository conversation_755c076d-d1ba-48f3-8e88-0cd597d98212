import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from '../../../../../play-comp-library/src/lib/components/sidebar/sidebar.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'test-sidebar',
  standalone: true,
  imports: [CommonModule, SidebarComponent, ButtonComponent, IconComponent],
  templateUrl: './test-sidebar.component.html',
  styleUrl: './test-sidebar.component.scss',
})
export class TestSidebarComponent {
   isSidebarCollapsed = false;
  activeItem = 'Dashboard';

  // Navigation items defined in the component logic
  navItems = [
    { icon: 'LayoutDashboard', label: 'Dashboard' },
    { icon: 'BarChart3', label: 'Requirement Analyser' },
    { icon: 'Shield', label: 'Test Case Scenario' },
    { icon: 'Plus', label: 'Test Case Creation' },
    { icon: 'FileText', label: 'Automation Script Creation' },
    { icon: 'GitBranch', label: 'Conversion' },
    { icon: 'Sliders', label: 'Test Optimization' },
    { icon: 'Database', label: 'Data Management' },
    { icon: 'Clock', label: 'Request History' },
    { icon: 'MessageCircle', label: 'Ask Me' },
  ];
}
