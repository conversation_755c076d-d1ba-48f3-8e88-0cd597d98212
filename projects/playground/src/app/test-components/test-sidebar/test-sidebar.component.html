<ava-sidebar [(isCollapsed)]="isSidebarCollapsed">
  <header class="sidebar-header-demo">
    <div class="logo">
      <span *ngIf="!isSidebarCollapsed">ASCENDION</span>
      <span *ngIf="isSidebarCollapsed">A</span>
    </div>

    <div class="search-container">
      <ava-icon iconName="Search" [iconSize]="20" iconColor="#9ca3af"></ava-icon>
      <input *ngIf="!isSidebarCollapsed" type="text" placeholder="Search" />
    </div>
  </header>

  <nav class="sidebar-nav-demo">
    <a 
      href="#" 
      *ngFor="let item of navItems" 
      class="nav-item" 
      [class.active]="item.label === activeItem"
      (click)="activeItem = item.label"
    >
      <ava-icon [iconName]="item.icon" [iconSize]="24"></ava-icon>
      <span *ngIf="!isSidebarCollapsed" class="nav-text">{{ item.label }}</span>
    </a>
  </nav>

  <div class="divider"></div>

  <footer class="sidebar-footer-demo">
    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face" alt="User Avatar" class="avatar"/>
    <div *ngIf="!isSidebarCollapsed" class="user-info">
      <span class="user-name">John Doe</span>
      <a href="#" class="user-action">View Profile</a>
    </div>
  </footer>
</ava-sidebar>

<main class="main-content">
  <h1>Main Application Content</h1>
  <p>This area is not part of the sidebar.</p>
</main>