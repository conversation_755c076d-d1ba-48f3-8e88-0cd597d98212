// Import your design system variables if you have them
$pink-500: #ec4899;
$gray-300: #d1d5db;
$text-color: #4b5563;

// By using ::ng-deep or keeping ViewEncapsulation.None in the sidebar,
// these parent styles can easily style the projected content.

.sidebar-header-demo {
  padding: 2rem;
  .logo {
    background-color: black;
    color: white;
    font-weight: bold;
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    display: inline-block;
    margin-bottom: 2rem;
    letter-spacing: 0.05em;
    // Collapsed styles for the logo
    ava-sidebar-container.collapsed & {
      padding: 0.75rem;
      margin-bottom: 1rem;
      font-size: 1.125rem;
    }
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid $gray-300;
    border-radius: 0.75rem;
    padding: 0.75rem;
    
    ava-icon {
      position: absolute;
      left: 1rem;
    }
    
    input {
      width: 100%;
      padding-left: 2rem;
      font-size: 1rem;
      border: none;
      outline: none;
      background: transparent;
    }

    ava-sidebar-container.collapsed & {
      justify-content: center;
      padding: 0.75rem;
      ava-icon { position: static; }
    }
  }
}

.sidebar-nav-demo {
  flex-grow: 1;
  padding: 0 2rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  ava-sidebar-container.collapsed & {
    padding: 0 1rem;
  }

  .nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.75rem;
    cursor: pointer;
    text-decoration: none;
    color: $text-color;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f9fafb; // gray-50
    }
    
    &.active {
      background-color: $pink-500;
      color: white;
    }

    .nav-text {
      font-size: 1.125rem;
      font-weight: 500;
    }

    ava-sidebar-container.collapsed & {
      justify-content: center;
    }
  }
}

.divider {
  border-top: 1px solid $gray-300;
  margin: 1.5rem 2rem;
  ava-sidebar-container.collapsed & {
    margin: 1.5rem 1rem;
  }
}

.sidebar-footer-demo {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0 2rem 2rem;
  
  .avatar {
    width: 4rem;
    height: 4rem;
    border-radius: 9999px;
    object-fit: cover;
  }

  .user-info {
    display: flex;
    flex-direction: column;
    .user-name {
      font-size: 1.25rem;
      font-weight: bold;
      color: black;
    }
    .user-action {
      font-size: 1rem;
      font-weight: 500;
      color: $pink-500;
      text-decoration: none;
      &:hover { text-decoration: underline; }
    }
  }

  ava-sidebar-container.collapsed & {
    justify-content: center;
  }
}